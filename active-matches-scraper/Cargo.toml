[package]
name = "active-matches-scraper"
version = "0.1.0"
edition = "2024"

[dependencies]
clickhouse = { version = "0.13.2", features = ["time"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_repr = "0.1.20"
tokio = { version = "1.45.0", features = ["rt-multi-thread", "macros"] }
reqwest = { version = "0.12.15", features = ["json"] }
delay_map = "0.4.1"
metrics = "0.24"
tracing = "0.1.41"
common = { version = "0.1.0", path = "../common" }
anyhow = "1.0.98"

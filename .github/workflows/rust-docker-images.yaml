name: Rust Docker Images

on:
  workflow_dispatch:
  push:
    branches:
      - 'master'
    paths:
      - 'Dockerfile'
      - 'Cargo.toml'
      - 'Cargo.lock'
      - 'common/**'
      - 'salt-scraper/**'
      - 'hltv-scraper/**'
      - 'ingest-worker/**'
      - 'matchdata-downloader/**'
      - 'active-matches-scraper/**'
      - 'builds-fetcher/**'
      - 'history-fetcher/**'
      - 'steam-profile-fetcher/**'
      - 'update-assets-tables/**'

permissions:
  contents: read
  packages: write
  id-token: write

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-${{ github.ref }}

jobs:
  build-base:
    runs-on: ubuntu-24.04-arm
    outputs:
      image-id: ${{ steps.build-base.outputs.imageid }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: '0'

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build Base Image
        id: build-base
        uses: docker/build-push-action@v6
        with:
          platforms: linux/arm64
          push: false
          target: builder-base
          tags: ghcr.io/${{ github.repository }}/rust-base:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
          outputs: type=docker,dest=/tmp/rust-base.tar
      - name: Upload Base Image
        uses: actions/upload-artifact@v4
        with:
          name: rust-base-image
          path: /tmp/rust-base.tar
          retention-days: 1

  build-and-push-components:
    runs-on: ubuntu-24.04-arm
    needs: build-base
    strategy:
      matrix:
        component:
          - salt-scraper
          - hltv-scraper
          - ingest-worker
          - matchdata-downloader
          - active-matches-scraper
          - builds-fetcher
          - history-fetcher
          - steam-profile-fetcher
          - update-assets-tables
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: '0'

      - name: Filter changed paths
        id: changes
        uses: dorny/paths-filter@v3
        with:
          filters: |
            change: [ '${{ matrix.component }}/**', 'common/**', 'Cargo.toml', 'Cargo.lock', 'Dockerfile' ]

      - name: Check if component should build
        id: should_build
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" || "${{ steps.changes.outputs['change'] }}" == "true" ]]; then
            echo "build=true" >> $GITHUB_OUTPUT
          else
            echo "build=false" >> $GITHUB_OUTPUT
          fi

      - name: Set up Docker Buildx
        if: steps.should_build.outputs.build == 'true'
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        if: steps.should_build.outputs.build == 'true'
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Download Base Image
        if: steps.should_build.outputs.build == 'true'
        uses: actions/download-artifact@v4
        with:
          name: rust-base-image
          path: /tmp

      - name: Load Base Image
        if: steps.should_build.outputs.build == 'true'
        run: |
          docker load --input /tmp/rust-base.tar

      - name: Build and Push Component
        if: steps.should_build.outputs.build == 'true'
        run: |
          echo "Building and pushing ${{ matrix.component }}"
          docker buildx build \
            --platform linux/arm64 \
            --target runtime \
            --build-arg EXE_NAME=${{ matrix.component }} \
            --tag ghcr.io/${{ github.repository }}/${{ matrix.component }}:latest \
            --cache-from type=gha \
            --cache-to type=gha,mode=max \
            --push \
            .

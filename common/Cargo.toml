[package]
name = "common"
version = "0.1.0"
edition = "2024"

[dependencies]
base64 = "0.22.1"
once_cell = "1.21.3"
reqwest = { version = "0.12.15", features = ["json"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
valveprotos = { git = "https://github.com/deadlock-api/valveprotos-rs.git", rev = "837747825a529673e97b681ee0f70b82d13eb0eb", features = ["gc-client", "serde"] }
prost = "0.13.5"
metrics = "0.24.2"
tracing = "0.1.41"
clickhouse = "0.13.2"
object_store = { version = "0.12.1", features = ["aws"] }
anyhow = "1.0.98"
sqlx = { version = "0.8.6", features = ["macros", "postgres", "runtime-tokio", "time"] }
fred = { version = "10.1.0", features = ["i-hexpire"] }
metrics-exporter-prometheus = "0.17"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }

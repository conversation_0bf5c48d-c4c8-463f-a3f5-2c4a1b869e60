[package]
name = "valveprotos"
version = "0.0.0"
edition = "2024"

[lib]
path = "lib.rs"

[dependencies]
prost = "0.13.5"
prost-wkt = { version = "0.6.1", optional = true }
prost-wkt-types = { version = "0.6.1", optional = true }
serde = { version = "1.0.219", optional = true, features = ["derive"] }
ureq = { version = "3.0.11", features = ["json"], optional = true }

[build-dependencies]
heck = "0.5.0"
prost-build = "0.13.5"
prost-types = "0.13.5"
prost-wkt-build = { version = "0.6.1", optional = true }

[features]
gc-common = []
gc-client = ["gc-common"]
game-msgs = ["gc-common"]
user-msgs = ["gc-common"]
serde = ["dep:prost-wkt", "dep:prost-wkt-types", "dep:prost-wkt-build", "dep:serde"]
requests = ["dep:ureq"]

[[bin]]
name = "fetch-protos"
path = "scripts/fetch-protos.rs"
required-features = ["requests"]

[[bin]]
name = "graph-imports"
path = "scripts/graph-imports.rs"

[package]
name = "steam-profile-fetcher"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.98"
arl = "0.2.0"
clickhouse = "0.13.2"
common = { path = "../common" }
itertools = "0.14.0"
metrics = "0.24.2"
once_cell = "1.21.3"
rand = "0.9.1"
reqwest = { version = "0.12.15", features = ["json"] }
serde = { version = "1.0.219", features = ["derive"] }
sqlx = { version = "0.8.6", features = ["runtime-tokio", "postgres", "chrono"] }
tokio = { version = "1.45.0", features = ["rt-multi-thread", "macros"] }
tracing = "0.1.41"
